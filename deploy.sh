#!/bin/bash

# Proxy Service Deployment Script
# Usage: ./deploy.sh [username@]hostname [ssh_password]

set -e

# Configuration
SERVICE_NAME="proxy-service"
SERVICE_FILE="proxy-service.py"
REMOTE_DIR="/opt/proxy-service"
SERVICE_PORT=3000
PYTHON_BIN="/usr/bin/python3"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required parameters are provided
if [ $# -lt 1 ]; then
    print_error "Usage: $0 [username@]hostname [ssh_password]"
    print_error "Example: $0 root@************* mypassword"
    print_error "Example: $0 <EMAIL> (will prompt for password)"
    exit 1
fi

SERVER="$1"
SSH_PASSWORD="$2"

# Check if proxy-service.py exists
if [ ! -f "$SERVICE_FILE" ]; then
    print_error "File $SERVICE_FILE not found in current directory"
    exit 1
fi

# Check if sshpass is installed
if ! command -v sshpass &> /dev/null; then
    print_error "sshpass is not installed. Please install it first:"
    print_error "  Ubuntu/Debian: sudo apt-get install sshpass"
    print_error "  CentOS/RHEL: sudo yum install sshpass"
    print_error "  macOS: brew install sshpass"
    exit 1
fi

# Function to execute remote commands
execute_remote() {
    local cmd="$1"
    if [ -n "$SSH_PASSWORD" ]; then
        sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no "$SERVER" "$cmd"
    else
        ssh -o StrictHostKeyChecking=no "$SERVER" "$cmd"
    fi
}

# Function to copy files to remote server
copy_file() {
    local local_file="$1"
    local remote_path="$2"
    if [ -n "$SSH_PASSWORD" ]; then
        sshpass -p "$SSH_PASSWORD" scp -o StrictHostKeyChecking=no "$local_file" "$SERVER:$remote_path"
    else
        scp -o StrictHostKeyChecking=no "$local_file" "$SERVER:$remote_path"
    fi
}

print_status "Starting deployment of $SERVICE_NAME to $SERVER"

# Step 1: Check if service is running and stop it
print_status "Checking if service is already running..."
if execute_remote "systemctl is-active --quiet $SERVICE_NAME" 2>/dev/null; then
    print_warning "Service $SERVICE_NAME is running. Stopping it..."
    execute_remote "sudo systemctl stop $SERVICE_NAME"
    print_status "Service stopped"
else
    print_status "Service is not running"
fi

# Step 2: Create directory structure
print_status "Creating directory structure..."
execute_remote "sudo mkdir -p $REMOTE_DIR"
execute_remote "sudo mkdir -p /var/log/$SERVICE_NAME"

# Step 3: Copy the Python file
print_status "Copying $SERVICE_FILE to server..."
copy_file "$SERVICE_FILE" "/tmp/$SERVICE_FILE"
execute_remote "sudo mv /tmp/$SERVICE_FILE $REMOTE_DIR/"
execute_remote "sudo chmod +x $REMOTE_DIR/$SERVICE_FILE"

# Step 4: Install Python dependencies
print_status "Installing Python dependencies..."
execute_remote "sudo apt-get update -qq" || execute_remote "sudo yum update -y -q" || true
execute_remote "sudo apt-get install -y python3 python3-pip" || execute_remote "sudo yum install -y python3 python3-pip" || true

# The imports you mentioned are all standard library modules, so no additional pip packages needed
print_status "All required modules are part of Python standard library"

# Step 5: Create systemd service file
print_status "Creating systemd service file..."
execute_remote "sudo tee /etc/systemd/system/$SERVICE_NAME.service > /dev/null << 'EOF'
[Unit]
Description=Proxy Service
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$REMOTE_DIR
ExecStart=$PYTHON_BIN $REMOTE_DIR/$SERVICE_FILE
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# Environment variables
Environment=PYTHONUNBUFFERED=1
Environment=PORT=$SERVICE_PORT

[Install]
WantedBy=multi-user.target
EOF"

# Step 6: Create a simple configuration file (optional)
print_status "Creating configuration file..."
execute_remote "sudo tee $REMOTE_DIR/config.json > /dev/null << 'EOF'
{
    \"port\": $SERVICE_PORT,
    \"log_level\": \"INFO\",
    \"service_name\": \"$SERVICE_NAME\"
}
EOF"

# Step 7: Set proper permissions
print_status "Setting permissions..."
execute_remote "sudo chown -R root:root $REMOTE_DIR"
execute_remote "sudo chmod -R 755 $REMOTE_DIR"

# Step 8: Reload systemd and enable service
print_status "Reloading systemd configuration..."
execute_remote "sudo systemctl daemon-reload"
execute_remote "sudo systemctl enable $SERVICE_NAME"

# Step 9: Start the service
print_status "Starting $SERVICE_NAME service..."
execute_remote "sudo systemctl start $SERVICE_NAME"

# Step 10: Check service status
print_status "Checking service status..."
sleep 2
if execute_remote "systemctl is-active --quiet $SERVICE_NAME"; then
    print_status "✓ Service $SERVICE_NAME is running successfully on port $SERVICE_PORT"
    
    # Show service status
    print_status "Service status:"
    execute_remote "sudo systemctl status $SERVICE_NAME --no-pager -l"
    
    # Show recent logs
    print_status "Recent logs:"
    execute_remote "sudo journalctl -u $SERVICE_NAME --no-pager -l -n 10"
    
    # Test if port is listening
    if execute_remote "ss -tuln | grep :$SERVICE_PORT" >/dev/null 2>&1; then
        print_status "✓ Service is listening on port $SERVICE_PORT"
    else
        print_warning "Service is running but may not be listening on port $SERVICE_PORT"
    fi
else
    print_error "✗ Service failed to start"
    print_error "Check logs with: sudo journalctl -u $SERVICE_NAME -f"
    exit 1
fi

print_status "Deployment completed successfully!"
print_status ""
print_status "Useful commands for managing the service:"
print_status "  Start:   sudo systemctl start $SERVICE_NAME"
print_status "  Stop:    sudo systemctl stop $SERVICE_NAME"
print_status "  Restart: sudo systemctl restart $SERVICE_NAME"
print_status "  Status:  sudo systemctl status $SERVICE_NAME"
print_status "  Logs:    sudo journalctl -u $SERVICE_NAME -f"
print_status "  Test:    curl http://localhost:$SERVICE_PORT"