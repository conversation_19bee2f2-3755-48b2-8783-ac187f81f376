#!/usr/bin/env python3
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse
import json
import logging
import re
import ipaddress
import random
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProxyHandler(BaseHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # Initialize bot detection patterns
        self.init_bot_patterns()
        self.init_ip_ranges()
        super().__init__(*args, **kwargs)
    
    def init_bot_patterns(self):
        """Initialize bot detection patterns"""
        # Search engine bots (legitimate crawlers)
        self.search_engine_bots = [
            r'googlebot',
            r'bingbot',
            r'slurp',  # Yahoo
            r'duckduckbot',
            r'baiduspider',
            r'yandexbot',
            r'facebookexternalhit',
            r'twitterbot',
            r'linkedinbot',
            r'pinterest',
            r'applebot',
            r'google-structured-data-testing-tool',
            r'google-site-verification',
            r'google-read-aloud',  # Google Read-Aloud service
            r'google.*read.*aloud',  # Broader pattern for Google Read-Aloud
            r'compatible.*google.*read.*aloud'  # Pattern for the specific UA you saw
        ]
        
        # Security scanners and malicious bots
        self.security_scanners = [
            r'nmap',
            r'masscan',
            r'zmap',
            r'shodan',
            r'censys',
            r'shadowserver',
            r'criminalip',
            r'scanner',
            r'vulnerability',
            r'pentest',
            r'exploit',
            r'sqlmap',
            r'nikto',
            r'dirb',
            r'gobuster',
            r'dirbuster'
        ]
        
        # Suspicious/unwanted crawlers
        self.suspicious_crawlers = [
            r'serpstat',
            r'ahrefsbot',
            r'semrushbot',
            r'mj12bot',
            r'dotbot',
            r'blexbot',
            r'petalbot',
            r'yisouspider',
            r'zgrab',
            r'masscan',
            r'stretchoid',
            r'deepfield'
        ]

        # Generic bot patterns (catch-all for any bot-like behavior)
        self.generic_bot_patterns = [
            r'bot\b',           # Any word ending with 'bot'
            r'crawler',         # Any crawler
            r'spider',          # Any spider
            r'scraper',         # Any scraper
            r'fetcher',         # Any fetcher
            r'indexer',         # Any indexer
            r'monitor',         # Monitoring tools
            r'checker',         # Checking tools
            r'validator',       # Validation tools
            r'archiver',        # Archiving tools
            r'harvester',       # Data harvesting tools
            r'extractor',       # Data extraction tools
            r'aggregator',      # Content aggregators
            r'feed.*reader',    # Feed readers
            r'rss.*reader',     # RSS readers
            r'news.*reader',    # News readers
            r'site.*monitor',   # Site monitoring
            r'uptime.*monitor', # Uptime monitoring
            r'link.*check',     # Link checkers
            r'broken.*link',    # Broken link checkers
            r'seo.*tool',       # SEO tools
            r'rank.*track',     # Ranking trackers
            r'keyword.*track',  # Keyword trackers
            r'analytics',       # Analytics tools
            r'metrics',         # Metrics collection
            r'benchmark',       # Benchmarking tools
            r'test.*tool',      # Testing tools
            r'load.*test',      # Load testing
            r'stress.*test',    # Stress testing
            r'performance.*test', # Performance testing
            r'api.*client',     # API clients
            r'http.*client',    # HTTP clients
            r'web.*client',     # Web clients
            r'automation',      # Automation tools
            r'headless',        # Headless browsers
            r'phantom',         # PhantomJS
            r'selenium',        # Selenium
            r'puppeteer',       # Puppeteer
            r'playwright',      # Playwright
            r'chrome.*headless', # Headless Chrome
            r'firefox.*headless', # Headless Firefox
        ]
        
        # Compile regex patterns for efficiency
        self.search_engine_pattern = re.compile('|'.join(self.search_engine_bots), re.IGNORECASE)
        self.security_scanner_pattern = re.compile('|'.join(self.security_scanners), re.IGNORECASE)
        self.suspicious_crawler_pattern = re.compile('|'.join(self.suspicious_crawlers), re.IGNORECASE)
        self.generic_bot_pattern = re.compile('|'.join(self.generic_bot_patterns), re.IGNORECASE)
    
    def init_ip_ranges(self):
        """Initialize known IP ranges for different services"""
        # Google IP ranges (simplified - in production, fetch from Google's API)
        self.google_ip_ranges = [
            '***********/19',
            '***********/27',
            '************/19',
            '***********/18',
            '************/17',
            '************/19'
        ]
        
        # Microsoft/Bing IP ranges
        self.microsoft_ip_ranges = [
            '***********/24',
            '***********/24',
            '***********/24'
        ]
        
        # Known malicious/scanner IP ranges from your CSV data
        self.suspicious_ip_ranges = [
            '*************/24',  # criminalip.com
            '************/24',   # censys
            '************/24',   # censys
            '*************/24',  # shadowserver
            '*************/24'   # scanner.modat.io
        ]
    
    def do_GET(self):
        if self.path == '/route':
            user_agent = user_agent = self.headers.get('X-User-Agent', '') or self.headers.get('User-Agent', '')
            original_uri = self.headers.get('X-Original-URI', '')
            remote_addr = self.headers.get('X-Real-IP', '')
            #logger.info(f"Headers received - X-User-Agent: '{self.headers.get('X-User-Agent', 'NOT_SET')}', User-Agent: '{self.headers.get('User-Agent', 'NOT_SET')}', X-Real-IP: '{remote_addr}', X-Original-URI: '{original_uri}'")
            
            # Get the proxy port based on routing logic
            proxy_port, bot_type = self.get_proxy_port(user_agent, original_uri, remote_addr)
            
            # Log the routing decision with more detail
            self.log_routing_decision(user_agent, original_uri, remote_addr, proxy_port, bot_type)
            
            # Return the port in the response header
            self.send_response(200)
            self.send_header('X-Pro-Po', str(proxy_port))
            self.send_header('X-Bot-Type', str(proxy_port))
            self.send_header('Content-Type', 'text/plain')
            self.end_headers()
            self.wfile.write(f'Port: {proxy_port}, Type: {bot_type}'.encode())
        elif self.path == '/health':
            # Health check endpoint
            self.send_response(200)
            self.send_header('Content-Type', 'text/plain')
            self.end_headers()
            self.wfile.write(b'OK')
        elif self.path == '/stats':
            # Simple stats endpoint (in production, you might want to track more metrics)
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            stats = {
                'status': 'running',
                'timestamp': datetime.now().isoformat(),
                'version': '2.0'
            }
            self.wfile.write(json.dumps(stats).encode())
        else:
            self.send_response(404)
            self.end_headers()
    
    def get_proxy_port(self, user_agent, uri, remote_addr):
        """
        Enhanced routing logic with multiple detection methods

        Args:
            user_agent (str): User-Agent header from the request
            uri (str): Original request URI
            remote_addr (str): Client IP address

        Returns:
            tuple: (port_number, bot_type)
        """
        if not user_agent and not remote_addr:
            return '3000', 'unknown'

        user_agent_lower = user_agent.lower() if user_agent else ''

        # 1. Check for security scanners first (highest priority - block/honeypot)
        if self.is_security_scanner(user_agent_lower, remote_addr, uri):
            logger.warning(f'Security scanner detected: UA="{user_agent[:100]}" IP="{remote_addr}" URI="{uri}"')
            return '9999', 'security_scanner'  # Redirect to honeypot/block port

        # 2. Check for legitimate search engine bots
        if self.is_legitimate_search_bot(user_agent_lower, remote_addr):
            logger.info(f'Legitimate search bot: UA="{user_agent[:100]}" IP="{remote_addr}"')
            return '8080', 'search_engine'  # SEO-friendly content port

        # 3. Check for suspicious crawlers
        if self.is_suspicious_crawler(user_agent_lower, remote_addr):
            logger.info(f'Suspicious crawler detected: UA="{user_agent[:100]}" IP="{remote_addr}"')
            return '8080', 'suspicious_crawler'  # Limited content port

        # 4. Check for generic bot patterns (catch-all for any bot-like behavior)
        if self.is_generic_bot(user_agent_lower):
            logger.info(f'Generic bot detected: UA="{user_agent[:100]}" IP="{remote_addr}"')
            return '8080', 'generic_bot'

        # 5. Check for specific paths that might indicate automated behavior
        if self.is_suspicious_path(uri):
            logger.info(f'Suspicious path accessed: URI="{uri}" IP="{remote_addr}"')
            return '8080', 'suspicious_path'

        # 6. Check for empty or suspicious User-Agent patterns
        if self.is_suspicious_user_agent(user_agent):
            logger.info(f'Suspicious user agent: UA="{user_agent}" IP="{remote_addr}"')
            return '8080', 'suspicious_ua'

        # 7. Check IP reputation (but allow private IPs to continue)
        ip_reputation = self.check_ip_reputation(remote_addr)
        if ip_reputation not in ['clean', 'private']:
            logger.info(f'Suspicious IP reputation: IP="{remote_addr}" Reputation="{ip_reputation}"')
            return '8080', f'ip_{ip_reputation}'

        # 8. Check for programmatic access patterns
        if self.is_programmatic_access(user_agent, uri, remote_addr):
            logger.info(f'Programmatic access detected: UA="{user_agent[:100]}" IP="{remote_addr}"')
            return '8080', 'programmatic_access'

        # 9. Random routing for A/B testing (intentional)
        if random.random() > 0.3:
            return '8081', 'regular_user_random'

        # 10. Default for regular users (only reached if all bot checks pass)
        return '8081', 'regular_user'
    
    def is_security_scanner(self, user_agent_lower, remote_addr, uri):
        """Detect security scanners and malicious bots"""
        # Check user agent patterns
        if self.security_scanner_pattern.search(user_agent_lower):
            return True
        
        # Check for suspicious IP ranges
        if self.is_ip_in_ranges(remote_addr, self.suspicious_ip_ranges):
            return True
        
        # Check for common scanner paths
        scanner_paths = [
            '/.env', '/wp-admin/', '/admin/', '/phpmyadmin/',
            '/wp-login.php', '/xmlrpc.php', '/wp-config.php',
            '/shell.php', '/c99.php', '/r57.php', '/webshell',
            '/.git/', '/.aws/', '/.ssh/', '/backup/',
            '/test.php', '/info.php', '/phpinfo.php'
        ]
        
        if uri and any(path in uri.lower() for path in scanner_paths):
            return True
        
        return False
    
    def is_legitimate_search_bot(self, user_agent_lower, remote_addr):
        """Verify legitimate search engine bots"""
        if not self.search_engine_pattern.search(user_agent_lower):
            return False
        
        # Additional verification for Google bots
        if 'googlebot' in user_agent_lower or 'google' in user_agent_lower:
            return self.verify_googlebot(remote_addr, user_agent_lower)
        
        # Additional verification for Bing bots
        if 'bingbot' in user_agent_lower:
            return self.verify_bingbot(remote_addr)
        
        # For other search engines, basic pattern matching is sufficient for now
        return True
    
    def verify_googlebot(self, remote_addr, user_agent_lower):
        """Verify if the request is actually from Google"""
        if not remote_addr:
            return False

        # Extended Google IP ranges (you should fetch these from Google's API in production)
        extended_google_ranges = self.google_ip_ranges + [
            '**********/20',     # Additional Google range that includes ************
            '**********/16',     # Google services
            '***********/16',    # Google services
            '***********/21',    # Google services
            '***********/16',    # Google services
            '***********/19',    # Google services
        ]

        # Check if IP is in known Google ranges
        if self.is_ip_in_ranges(remote_addr, extended_google_ranges):
            return True

        # Special handling for Google Read-Aloud and other Google services
        if any(pattern in user_agent_lower for pattern in ['google-read-aloud', 'google.*read.*aloud']):
            # Google Read-Aloud might come from different IP ranges
            # Be more permissive but log for monitoring
            logger.info(f'Google Read-Aloud detected from IP not in known ranges: IP={remote_addr}')
            return True

        # In production, you might want to do reverse DNS lookup
        # For now, we'll be permissive but log suspicious cases
        if 'googlebot' in user_agent_lower:
            logger.warning(f'Potential fake Googlebot: IP={remote_addr} not in known Google ranges')
            return False

        return True
    
    def verify_bingbot(self, remote_addr):
        """Verify if the request is actually from Bing"""
        if not remote_addr:
            return False
        
        return self.is_ip_in_ranges(remote_addr, self.microsoft_ip_ranges)
    
    def is_suspicious_crawler(self, user_agent_lower, remote_addr):
        """Detect suspicious but not necessarily malicious crawlers"""
        return self.suspicious_crawler_pattern.search(user_agent_lower) is not None

    def is_generic_bot(self, user_agent_lower):
        """Detect any generic bot patterns"""
        if not user_agent_lower:
            return False
        return self.generic_bot_pattern.search(user_agent_lower) is not None
    
    def is_suspicious_path(self, uri):
        """Check for suspicious request paths"""
        if not uri:
            return False
        
        suspicious_patterns = [
            r'\.php\?',
            r'union.*select',
            r'<script',
            r'javascript:',
            r'eval\(',
            r'base64_decode',
            r'\.\./\.\.',
            r'etc/passwd',
            r'proc/version'
        ]
        
        uri_lower = uri.lower()
        return any(re.search(pattern, uri_lower) for pattern in suspicious_patterns)
    
    def is_suspicious_user_agent(self, user_agent):
        """Check for suspicious user agent patterns"""
        if not user_agent:
            return True  # Empty user agent is suspicious

        if len(user_agent) < 10:
            return True  # Very short user agents are suspicious

        suspicious_ua_patterns = [
            r'^python',
            r'^curl',
            r'^wget',
            r'^libwww',
            r'^http',
            r'bot.*bot',  # Multiple 'bot' occurrences
            r'^mozilla/4\.0$',  # Minimal old Mozilla string
            r'scanner',
            r'spider.*spider',
            r'^java/',           # Java HTTP clients
            r'^go-http-client',  # Go HTTP clients
            r'^node-fetch',      # Node.js fetch
            r'^axios/',          # Axios HTTP client
            r'^requests/',       # Python requests library
            r'^urllib',          # Python urllib
            r'^okhttp',          # OkHttp client
            r'^apache-httpclient', # Apache HTTP client
            r'^ruby',            # Ruby HTTP clients
            r'^php/',            # PHP HTTP clients
            r'^perl/',           # Perl HTTP clients
            r'^postman',         # Postman API client
            r'^insomnia',        # Insomnia API client
            r'^httpie',          # HTTPie client
            r'^rest-client',     # REST clients
            r'^api-client',      # Generic API clients
        ]

        user_agent_lower = user_agent.lower()
        return any(re.search(pattern, user_agent_lower) for pattern in suspicious_ua_patterns)

    def is_programmatic_access(self, user_agent, uri, remote_addr):
        """Detect programmatic access patterns"""
        if not user_agent:
            return True

        user_agent_lower = user_agent.lower()

        # Check for missing common browser headers patterns
        # (This would need to be enhanced with actual header analysis)

        # Check for programmatic user agent patterns
        programmatic_patterns = [
            r'headless',
            r'automation',
            r'webdriver',
            r'selenium',
            r'puppeteer',
            r'playwright',
            r'phantom',
            r'chrome.*headless',
            r'firefox.*headless',
            r'safari.*headless',
            r'test.*browser',
            r'automated.*browser',
            r'robot',
            r'machine',
            r'script',
            r'tool',
            r'utility',
            r'service',
            r'daemon',
            r'worker',
            r'job',
            r'task',
            r'process',
            r'application',
            r'program',
            r'software',
            r'system',
            r'engine',
            r'framework',
            r'library',
            r'module',
            r'component',
            r'plugin',
            r'extension',
            r'addon',
            r'widget',
            r'embed',
            r'iframe',
            r'ajax',
            r'xhr',
            r'fetch',
            r'api',
            r'rest',
            r'json',
            r'xml',
            r'rpc',
            r'soap',
            r'graphql'
        ]

        # Check for very generic or minimal user agents that might be programmatic
        if len(user_agent) < 20 and any(pattern in user_agent_lower for pattern in ['mozilla', 'chrome', 'safari', 'firefox']):
            return True

        return any(re.search(pattern, user_agent_lower) for pattern in programmatic_patterns)
    
    def check_ip_reputation(self, remote_addr):
        """Basic IP reputation check"""
        if not remote_addr:
            return 'unknown'
        
        try:
            ip = ipaddress.ip_address(remote_addr)
            
            # Check for private IP ranges (usually legitimate)
            if ip.is_private:
                return 'private'
            
            # Check for known cloud providers (higher scrutiny)
            cloud_ranges = [
                '*******/8',       # AWS
                '********/8',      # Google Cloud
                '20.0.0.0/8',      # Microsoft Azure
                '**********/16',   # DigitalOcean
                '***********/16'   # Various VPS providers
            ]
            
            if self.is_ip_in_ranges(remote_addr, cloud_ranges):
                return 'cloud'
            
            return 'clean'
            
        except ValueError:
            return 'invalid'
    
    def is_ip_in_ranges(self, ip_str, ip_ranges):
        """Check if IP is in any of the given ranges"""
        if not ip_str:
            return False
        
        try:
            ip = ipaddress.ip_address(ip_str)
            for range_str in ip_ranges:
                if ip in ipaddress.ip_network(range_str, strict=False):
                    return True
        except (ValueError, ipaddress.AddressValueError):
            pass
        
        return False
    
    def log_routing_decision(self, user_agent, uri, remote_addr, proxy_port, bot_type):
        """Enhanced logging with structured information"""
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'ip': remote_addr,
            'user_agent': user_agent[:200] if user_agent else '',  # Truncate long UAs
            'uri': uri[:100] if uri else '',  # Truncate long URIs
            'routed_port': proxy_port,
            'bot_type': bot_type
        }
        
        if bot_type in ['security_scanner', 'suspicious_crawler']:
            logger.warning(f'BLOCKED/RESTRICTED: {json.dumps(log_data)}')
        else:
            logger.info(f'ROUTED: {json.dumps(log_data)}')
    
    def log_message(self, format, *args):
        # Custom logging to avoid default HTTP server logs
        pass

if __name__ == '__main__':
    try:
        server = HTTPServer(('127.0.0.1', 3000), ProxyHandler)
        logger.info('Enhanced proxy routing service started on http://127.0.0.1:8080')
        logger.info('Available endpoints: /route, /health, /stats')
        logger.info('Ready to handle routing requests from nginx with advanced bot detection')
        server.serve_forever()
    except KeyboardInterrupt:
        logger.info('Service stopped by user')
    except Exception as e:
        logger.error(f'Service error: {e}')
        raise
